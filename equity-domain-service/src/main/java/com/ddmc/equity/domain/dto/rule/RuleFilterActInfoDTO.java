package com.ddmc.equity.domain.dto.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/21 20:59
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class RuleFilterActInfoDTO {

    @ApiModelProperty(value = "活动 id")
    private Long activityId;

    @ApiModelProperty(value = "策略 id")
    private Long strategyId;

    @ApiModelProperty(value = "权益组 id")
    private Long benefitGroupId;

    @ApiModelProperty(value = "权益 id")
    private Long benefitId;

    /**
     * @see com.ddmc.equity.enums.BenefitUnableReceiveReasonType
     */
    @ApiModelProperty("不能领取原因 code")
    private String unableReceiveReasonCode;
}
