# 活动权益处理器实现要求

## 任务目标
实现活动权益相关的处理器，包括咨询活动和执行权益两个核心处理器。

## 前置依赖
- SceneActionProcessor 接口已定义
- SceneActionProcessContext 和 SceneActionProcessResult 已创建
- SceneActivityBenefitCoreService 活动权益核心服务已实现
- AccountStrategyContext 账户策略上下文已实现

## 需要完成的代码

### 1. 咨询活动处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.consult_activity.DefaultConsultActivityProcessor`

#### 类定义
- 实现 SceneActionProcessor 接口
- 使用 @Slf4j 和 @Component 注解

#### 方法实现

##### getProcessTypeEnum 方法
- 返回：SceneActionProcessTypeEnum.CONSULT_ACTIVITY

##### process 方法
- 参数：SceneActionProcessContext context
- 返回：SceneActionProcessResult
- 参考代码：com.ddmc.equity.domain.service.core.impl.UniversalBenefitOperateCoreServiceImpl#draw
- 处理流程：
  1. 创建咨询上下文
  2. 咨询活动信息，调用 com.ddmc.equity.domain.service.core.SceneActivityBenefitCoreService.consultSceneBenefit
  3. 转换为场景处理结果。如果 consultActivityList 不为空，返回咨询活动列表；如果 unableReceiveBenefitDTOList 不为空，返回咨询失败和不可领取的权益列表；如果 consultActivityList 和 unableReceiveBenefitDTOList 均为空，返回咨询失败，活动不存在或者活动状态异常

### 2. 执行权益处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.execute_bft.DefaultExecuteBftProcessor`

#### 类定义
- 实现 SceneActionProcessor 接口
- 使用 @Slf4j 和 @Component 注解

#### 方法实现

##### getProcessTypeEnum 方法
- 返回：SceneActionProcessTypeEnum.EXECUTE_BFT

##### shouldBreakAfterProcess 方法
- 参数：SceneActionProcessContext context, SceneActionProcessResult result
- 返回：boolean
- 实现：返回 false（执行权益不中断流程，继续执行下一个节点）

##### process 方法
- 参数：SceneActionProcessContext context
- 返回：SceneActionProcessResult
- 参考代码：com.ddmc.equity.domain.service.core.impl.UniversalBenefitOperateCoreServiceImpl.receive(com.ddmc.equity.domain.dto.DrawAndReceiveReqDTO, com.ddmc.equity.domain.dto.FullBenefitInfoDTO)
- 处理流程：
  1. 创建领取权益上下文，领取权益信息使用 context.getReceiveBenefitInfoDTO()
  2. 执行权益领取，调用 com.ddmc.equity.account.AccountStrategyContext#sceneReceiveNew
  3. 转换为场景处理结果。领取成功，返回成功，设置 executeBftResultStatus（成功）、accountDetailId、rpcResponseExtDTO；领取失败，返回失败，设置 executeBftResultStatus（失败）、unableReceiveBenefitDTOList（原因 BenefitUnableReceiveReasonType.EXECUTE_BFT_FAILURE）；领取处理中，返回失败，设置 executeBftResultStatus（处理中）、unableReceiveBenefitDTOList（原因 BenefitUnableReceiveReasonType.EXECUTE_BFT_PROCESSING）

## 注意事项

1. 中文、英文字母、数字之间需要留有一个空格
2. log 输出需要是英文
3. 错误信息要具体明确，便于问题排查
4. 监控打点使用 com.ddmc.equity.common.util.CsossUtils.logEventWithSpan 方法，type 使用统一的 MonitorConstants 常量 
5. 所有处理器都需要添加 @Component 注解 
6. 执行权益处理器的 shouldBreakAfterProcess 返回 false，不中断流程
