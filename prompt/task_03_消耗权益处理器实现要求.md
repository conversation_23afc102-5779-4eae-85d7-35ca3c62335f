# 消耗权益处理器实现要求

## 任务目标
实现消耗权益相关的处理器，包括抽象基类和三个具体处理器：检查、扣减、确认消耗权益。

## 前置依赖
- SceneActionProcessor 接口已定义
- SceneActionProcessContext 和 SceneActionProcessResult 已创建
- ConsumeBenefitStrategyFactory 策略工厂已实现
- ConsumeBenefitContext 和 ConsumeBenefitResult 已创建

## 需要完成的代码

### 1. 抽象消耗权益处理器基类
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.AbstractConsumeBenefitProcessor`

#### 类定义
- 抽象类，实现 SceneActionProcessor 接口
- 使用 @Slf4j 注解

#### 配置属性
```java
@Value("${scene.action.consume.benefit.required.scene.codes:SPIN_LOTTERY,POINT_EXCHANGE}")
private Set<String> consumeBenefitRequiredSceneCodes;

@Value("${scene.action.consume.benefit.supported.types:1,16,21}")
private Set<Integer> consumeBenefitSupportedTypes;
```

#### 依赖注入
```java
@Resource
protected ConsumeBenefitStrategyFactory strategyFactory;
```

#### 通用方法实现

##### isConsumeBenefitRequired 方法
- 访问修饰符：protected
- 参数：String sceneCode
- 返回：boolean
- 功能：判断指定活动场景是否需要消耗权益
- 实现：检查 consumeBenefitRequiredSceneCodes 是否包含 sceneCode

##### checkConsumeBenefit 方法
- 访问修饰符：protected
- 参数：SceneActionProcessContext context
- 返回：SceneActionProcessResult（校验成功返回 null，失败返回具体错误结果）
- 功能：检查消耗权益的基础参数有效性
- 校验逻辑：
  1. 检查消耗权益类型不能为空
  2. 检查消耗权益类型是否在支持列表中
  3. 检查消耗权益数量不能为空
  4. 检查消耗权益数量格式是否为有效数字
  5. 检查消耗权益数量是否大于 0
- 错误信息格式：
  - "消耗权益类型不能为空，请检查配置"
  - "不支持的消耗权益类型：{type}，请检查配置"
  - "消耗权益数量不能为空，请检查参数"
  - "消耗权益数量格式错误：{amount}，必须是有效数字"
  - "消耗权益数量必须大于 0，当前值：{amount}"

### 2. 检查消耗权益处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.check_consume_bft.DefaultCheckConsumeBftProcessor`

#### 类定义
- 继承 AbstractConsumeBenefitProcessor
- 使用 @Slf4j 和 @Component 注解

#### 方法实现

##### getProcessTypeEnum 方法
- 返回：SceneActionProcessTypeEnum.CHECK_CONSUME_BFT

##### isEnabled 方法
- 参数：SceneActionProcessContext context
- 返回：boolean
- 实现：调用 super.isConsumeBenefitRequired(context.getSceneCode())

##### process 方法
- 参数：SceneActionProcessContext context
- 返回：SceneActionProcessResult
- 处理流程：
  1. 检查消耗权益基础参数有效性（调用 checkConsumeBenefit） 。如果检查失败，记录 warn 日志并打点，返回检查结果
  2. 创建消耗权益上下文（调用 ConsumeBenefitContext.buildConsumeBenefitContext）
  3. 获取对应的权益策略（调用 strategyFactory.getStrategy）。如果策略为空，记录 warn 日志并打点，返回失败结果
  4. 执行消耗权益检查（调用 strategy.checkConsumeBenefit）
  5. 构建无法领取权益列表（根据 consumeBenefitResultCode 转换成对应的 unableReceiveReasonCode，使用 BenefitConvertEntity
     .convertToFullBenefitInfoDTOList(context.getBizData().getConsultActivityList()) 构建）
  6. 转换为场景处理结果（调用 ConsumeBenefitResult.convertToSceneActionProcessResult）

### 3. 扣减消耗权益处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.deduct_consume_bft.DefaultDeductConsumeBftProcessor`

#### 类定义
- 继承 AbstractConsumeBenefitProcessor
- 使用 @Slf4j 和 @Component 注解

#### 方法实现

##### getProcessTypeEnum 方法
- 返回：SceneActionProcessTypeEnum.DEDUCT_CONSUME_BFT

##### isEnabled 方法
- 参数：SceneActionProcessContext context
- 返回：boolean
- 实现：调用 super.isConsumeBenefitRequired(context.getSceneCode())

##### process 方法
- 参数：SceneActionProcessContext context
- 返回：SceneActionProcessResult
- 处理流程：
  1. 检查消耗权益基础参数有效性（调用 checkConsumeBenefit）。如果检查失败，记录 warn 日志并打点，返回检查结果
  2. 创建消耗权益上下文（调用 ConsumeBenefitContext.buildConsumeBenefitContext）
  3. 获取对应的权益策略（调用 strategyFactory.getStrategy）。如果策略为空，记录 warn 日志并打点，返回失败结果
  4. 执行消耗权益扣减（调用 strategy.deductConsumeBenefit）
  5. 构建无法领取权益列表（根据 consumeBenefitResultCode 转换成对应的 unableReceiveReasonCode，使用 context.getReceiveBenefitInfoDTO() 构建）
  6. 转换为场景处理结果（调用 ConsumeBenefitResult.convertToSceneActionProcessResult）

### 4. 确认消耗权益处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.confirm_consume_bft.DefaultConfirmConsumeBftProcessor`

#### 类定义
- 继承 AbstractConsumeBenefitProcessor
- 使用 @Slf4j 和 @Component 注解

#### 方法实现

##### getProcessTypeEnum 方法
- 返回：SceneActionProcessTypeEnum.CONFIRM_CONSUME_BFT

##### isEnabled 方法
- 参数：SceneActionProcessContext context
- 返回：boolean
- 实现：调用 super.isConsumeBenefitRequired(context.getSceneCode())

##### process 方法
- 参数：SceneActionProcessContext context
- 返回：SceneActionProcessResult
- 处理流程：
  1. 检查消耗权益基础参数有效性（调用 checkConsumeBenefit）。如果检查失败，记录 warn 日志并打点，返回检查结果
  2. 创建消耗权益上下文（调用 ConsumeBenefitContext.buildConsumeBenefitContext）
  3. 获取对应的权益策略（调用 strategyFactory.getStrategy）。如果策略为空，记录 warn 日志并打点，返回失败结果
  4. 判断前序步骤执行结果，决定确认扣减还是释放权益
     - 如果前序步骤成功：调用 strategy.confirmConsumeBenefit
     - 如果前序步骤失败：调用 strategy.releaseConsumeBenefit
  5. 返回成功结果（无论确认还是释放都返回成功）

##### 判断前序步骤执行结果逻辑
- 检查 context.getBizData() 中的执行权益结果状态
- 如果执行权益成功，则确认扣减
- 如果执行权益失败，则释放权益

## 注意事项

1. 中文、英文字母、数字之间需要留有一个空格
2. log 输出需要是英文
3. 错误信息要具体明确，便于问题排查 
4. 监控打点使用 com.ddmc.equity.common.util.CsossUtils.logEventWithSpan 方法，type 使用统一的 MonitorConstants 常量 
5. 所有处理器都需要添加 @Component 注解
6. 确认消耗权益处理器需要根据前序步骤结果决定确认还是释放
7. 所有方法都需要进行参数校验，复用抽象基类提供的校验方法
