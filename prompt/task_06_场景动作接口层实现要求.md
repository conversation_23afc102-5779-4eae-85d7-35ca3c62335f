# 场景动作接口层实现要求

## 任务目标
实现场景动作的完整接口层，包括 Feign 客户端、REST 控制器、应用服务接口和实现类，提供咨询和领取两个核心功能。

## 前置依赖
- SceneActionProcessEngine 流程引擎已实现
- SceneActionProcessContext 和 SceneActionProcessResult 已创建
- 相关的请求响应 DTO 已定义
- CustomerSceneActionConvertEntity 转换实体已实现

## 需要完成的代码

### 1. Feign 客户端接口
**路径**: `com.ddmc.equity.client.customer.CustomerSceneActionClient`

#### 类定义
- 接口类型，使用 @FeignClient 注解
- 服务名：equity-customer-service
- 路径：/scene/action/v1
- 上下文 ID：customerSceneActionClient

#### 接口方法

##### consult 方法（咨询）
- 注解：@ApiOperation("咨询")、@PostMapping("/consult")、@ResponseBody
- 参数：@Valid @RequestBody SceneActionConsultReqDTO req
- 返回：WebResp<SceneActionConsultRespDTO>
- 功能：咨询用户在指定场景下可领取的权益活动

##### receive 方法（领取）
- 注解：@ApiOperation("领取")、@PostMapping("/receive")、@ResponseBody
- 参数：@Valid @RequestBody SceneActionReceiveReqDTO req
- 返回：WebResp<SceneActionReceiveRespDTO>
- 功能：执行权益领取流程

### 2. REST 控制器
**路径**: `com.ddmc.equity.controller.CustomerSceneActionController`

#### 类定义
- 实现 CustomerSceneActionClient 接口
- 使用注解：@Slf4j、@Api(tags = "场景动作 V1 接口")、@RestController、@RequestMapping("/scene/action/v1")

#### 方法实现

##### consult 方法
- 重写接口方法，添加相同的注解
- 实现逻辑：
  1. 记录请求日志
  2. 调用应用服务执行咨询逻辑
  3. 包装为 WebResp 响应

##### receive 方法
- 重写接口方法，添加相同的注解
- 实现逻辑：
  1. 记录请求日志
  2. 调用应用服务执行领取逻辑
  3. 包装为 WebResp 响应

### 3. 应用服务接口
**路径**: `com.ddmc.equity.service.CustomerSceneActionAppService`

#### 接口定义
- 纯接口，提供场景咨询和领取功能

#### 方法定义

##### consult 方法
- 参数：SceneActionConsultReqDTO req
- 返回：SceneActionConsultRespDTO
- 功能：咨询用户在指定场景下可领取的权益活动

##### receive 方法
- 参数：SceneActionReceiveReqDTO req
- 返回：SceneActionReceiveRespDTO
- 功能：执行权益领取流程

### 4. 应用服务实现类
**路径**: `com.ddmc.equity.service.impl.CustomerSceneActionAppServiceImpl`

#### 类定义
- 实现 CustomerSceneActionAppService 接口
- 使用注解：@Slf4j、@Service

#### 方法实现

##### consult 方法
- 参数：SceneActionConsultReqDTO req
- 返回：SceneActionConsultRespDTO
- 实现流程：
  1. 参数校验和日志记录
  2. 构建咨询上下文（调用 CustomerSceneActionConvertEntity.buildConsultContext）
  3. 执行咨询流程（调用 sceneActionProcessEngine.executeConsultFlow）
  4. 转换为响应 DTO（调用 CustomerSceneActionConvertEntity.convertToConsultResponse）

##### receive 方法
- 参数：SceneActionReceiveReqDTO req
- 返回：SceneActionReceiveRespDTO
- 实现流程：
  1. 参数校验和日志记录
  2. 构建领取上下文（调用 CustomerSceneActionConvertEntity.buildReceiveContext）
  3. 执行领取流程（调用 sceneActionProcessEngine.executeReceiveFlow）
  4. 转换为响应 DTO（调用 CustomerSceneActionConvertEntity.convertToReceiveResponse）

## 注意事项

1. 中文、英文字母、数字之间需要留有一个空格
2. log 输出需要是英文
3. 错误信息要具体明确，便于问题排查
4. 监控打点使用 com.ddmc.equity.common.util.CsossUtils.logEventWithSpan 方法，type 使用统一的 MonitorConstants 常量
