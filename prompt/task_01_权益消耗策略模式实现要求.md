# 权益消耗策略模式实现要求

## 任务目标
实现权益消耗的策略模式，支持不同权益类型的消耗逻辑（积分、活动次数等），包括检查、扣减、确认、释放四个操作阶段。

## 前置依赖
- ConsumeBenefitContext 和 ConsumeBenefitResult 已创建
- 相关的 DTO 类已创建（ConsumeBenefitBizParamsDTO、ConsumeBenefitBizDataDTO）

## 需要完成的代码

### 1. 策略接口
**路径**: `com.ddmc.equity.processor.consume_benefit.strategy.ConsumeBenefitStrategy`

定义五个方法：
- getSupportedConsumeBenefitType：获取支持的消耗权益类型。返回 Integer（权益类型）
- checkConsumeBenefit：检查用户可用权益数量是否足够。参数 ConsumeBenefitContext，返回 ConsumeBenefitResult
- deductConsumeBenefit：扣减用户可用权益数量（冻结或直接扣减）。参数 ConsumeBenefitContext，返回 ConsumeBenefitResult
- confirmConsumeBenefit：确认扣减用户可用权益数量（成功时确认扣减）。参数 ConsumeBenefitContext，返回 ConsumeBenefitResult
- releaseConsumeBenefit：释放用户可用权益数量（失败时释放）。参数 ConsumeBenefitContext，返回 ConsumeBenefitResult

### 2. 抽象策略基类
**路径**: `com.ddmc.equity.processor.consume_benefit.strategy.AbstractConsumeBenefitStrategy`

实现 ConsumeBenefitStrategy 接口，提供通用的参数校验方法：
参数 ConsumeBenefitContext，返回 ConsumeBenefitResult。校验成功返回 null，校验失败返回，ConsumeBenefitResult.paramsErrorFailure("具体错误信息", null)
- checkParams：校验基本参数（用户 ID、消耗权益数量）
- checkParamsWithReqNo：校验基本参数（用户 ID、消耗权益数量、流水号）
- checkParamsWithFreezeLogId：校验基本参数（用户 ID、消耗权益数量）

### 3. 具体策略实现
**路径**: `com.ddmc.equity.processor.consume_benefit.strategy.impl`

#### DefaultConsumeBenefitStrategy
- 继承 AbstractConsumeBenefitStrategy
- getSupportedConsumeBenefitType 返回 null（默认策略不支持特定类型）
- 所有操作方法直接返回成功结果
- 作为兜底策略使用

#### PointConsumeBenefitStrategy
- 继承 AbstractConsumeBenefitStrategy
- getSupportedConsumeBenefitType 返回 BenefitTypeEnum.POINT.getId()
- checkConsumeBenefit：调用积分服务查询用户可用积分，判断是否足够
  1. 先 checkParams
  2. 然后调用 com.ddmc.equity.infra.rpc.user_point.UserPointProxy#getUserTotalPoint，获取用户可用积分
  3. 判断 availableAmount 是否为空
  4. 判断 availableAmount 是否小于 context.getConsumeBenefitAmount()
- deductConsumeBenefit：调用积分服务冻结用户积分，返回冻结记录 ID
  1. 先 checkParamsWithReqNo
  2. 然后调用 com.ddmc.equity.infra.rpc.user_point.PointUserClientProxy#userPointFreeze，冻结用户积分
  3. 判断 冻结用户积分异常（resp == null || resp.getStatus() == null || resp.getStatus() == RpcRespDTO.RpcStatus.EXCEPTION），返回异常结果
  4. 判断 冻结用户积分成功（resp.getStatus() == RpcRespDTO.RpcStatus.SUCCESS && resp.getData() != null && resp.getData().getLogId() != null），返回成功结果（和冻结记录 ID）
  5. 判断 用户可用积分数量不足，code = "5"（resp.getStatus() == RpcRespDTO.RpcStatus.FAILURE && resp.getCode().equals("5")），返回用户可用积分数量不足
  6. 其他情况，返回冻结用户积分失败
- confirmConsumeBenefit：调用积分服务确认扣减积分（基于冻结记录 ID）
  1. 先 checkParamsWithFreezeLogId
  2. 然后调用 com.ddmc.equity.infra.rpc.user_point.PointUserClientProxy#userPointDeduct，确认扣减积分
  3. 判断 确认扣减积分异常（resp == null || resp.getStatus() == null || resp.getStatus() == RpcRespDTO.RpcStatus.EXCEPTION），返回异常结果
  4. 判断 确认扣减积分成功（resp.getStatus() == RpcRespDTO.RpcStatus.SUCCESS），返回成功结果
  5. 其他情况，返回确认扣减积分失败
- releaseConsumeBenefit：调用积分服务解冻积分（基于冻结记录 ID）
  1. 先 checkParamsWithFreezeLogId
  2. 然后调用 com.ddmc.equity.infra.rpc.user_point.PointUserClientProxy#userPointUnfreeze，解冻积分
  3. 判断 解冻积分异常（resp == null || resp.getStatus() == null || resp.getStatus() == RpcRespDTO.RpcStatus.EXCEPTION），返回异常结果
  4. 判断 解冻积分成功（resp.getStatus() == RpcRespDTO.RpcStatus.SUCCESS），返回成功结果
  5. 其他情况，返回解冻积分失败

### 4. 策略工厂
**路径**: `com.ddmc.equity.processor.consume_benefit.strategy.ConsumeBenefitStrategyFactory`

- 使用 @Component 注解，Spring 自动管理
- 注入 List<ConsumeBenefitStrategy> strategies，自动收集所有策略实现
- 使用 Map<Integer, ConsumeBenefitStrategy> 缓存策略映射关系
- @PostConstruct 方法初始化策略映射，遍历所有策略调用 getSupportedConsumeBenefitType 建立映射
- getStrategy 方法根据权益类型返回对应策略，找不到返回 null

## 注意事项
1. 中文、英文字母、数字之间需要留有一个空格
2. log 输出需要是英文
3. 错误信息要具体明确，便于问题排查
4. 监控打点使用 com.ddmc.equity.common.util.CsossUtils.logEventWithSpan 方法，type 使用统一的 MonitorConstants 常量 
5. 四个核心方法都需要进行参数校验，可以复用抽象基类提供的校验方法