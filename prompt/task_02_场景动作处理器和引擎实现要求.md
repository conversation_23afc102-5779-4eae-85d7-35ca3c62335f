# 场景动作处理器和引擎实现要求

## 任务目标
实现场景动作流程的处理器模式和流程引擎，支持不同场景的业务流程编排，包括咨询流程和领取流程的完整生命周期管理。

## 前置依赖
- SceneActionProcessContext 和 SceneActionProcessResult 已创建
- SceneActionProcessTypeEnum 流程类型枚举已定义
- 相关的 DTO 类已创建（SceneActionBizParamsDTO、SceneActionBizDataDTO）

## 需要完成的代码

### 1. 处理器接口
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.SceneActionProcessor`

定义六个方法：
- getSceneCode：获取活动场景码，默认返回 "default"
- getProcessTypeEnum：获取处理器类型，返回 SceneActionProcessTypeEnum（必须实现）
- isEnabled：是否启用该节点，参数 SceneActionProcessContext，返回 boolean，默认返回 true
- process：处理逻辑，参数 SceneActionProcessContext，返回 SceneActionProcessResult（必须实现）
- shouldChangeResult：判断该节点执行完是否需要更新流程结果，参数 SceneActionProcessContext，返回 boolean，默认返回 true
- shouldBreakAfterProcess：判断该节点执行完是否需要中断流程，参数 SceneActionProcessContext 和 SceneActionProcessResult，返回 boolean，默认逻辑为不成功时中断

### 4. 流程引擎
**路径**: `com.ddmc.equity.processor.scene_action.v1.engine.SceneActionProcessEngine`

- 使用 @Component 注解，Spring 自动管理
- 构造函数注入 List<SceneActionProcessor> processors，自动收集所有处理器实现
- 使用 Map<String, SceneActionProcessor> processorMap 缓存处理器映射关系
- 处理器映射 key 格式：sceneCode + "_" + processType

核心方法：
- executeConsultFlow：执行咨询流程，参数 SceneActionProcessContext 和 consultFlowType
- executeReceiveFlow：执行领取流程，参数 SceneActionProcessContext
- executeFlow：私有方法，执行具体流程编排逻辑
- getProcessor：获取处理器（优先场景特定，然后默认）
- executeProcessorLogic：执行处理器的业务逻辑，包含异常处理和监控打点

流程编排逻辑：
1. 遍历流程定义中的每个步骤
2. 获取对应的处理器（优先场景特定，回退到默认）
3. 检查是否启用该节点（调用 isEnabled）
4. 执行处理逻辑（调用 process）
5. 将当前步骤结果数据合并到上下文
6. 判断是否需要更新流程结果（调用 shouldChangeResult）
7. 判断是否需要中断流程（调用 shouldBreakAfterProcess）
8. 异常处理和最终结果构建

## 流程类型枚举

基于 SceneActionProcessTypeEnum：
- CONSULT_ACTIVITY：咨询活动
- CHECK_CONSUME_BFT：检查用户可用权益数量是否足够
- CHECK_RISK_BEFORE：检查前置风控
- DEDUCT_CONSUME_BFT：扣减用户可用权益数量
- EXECUTE_BFT：执行权益
- CONFIRM_CONSUME_BFT：确认用户可用权益数量
- REPORT_RISK_AFTER：上报后置风控

## 流程定义

### 咨询流程
1. CONSULT_ACTIVITY（咨询活动）

### 领取流程
1. CONSULT_ACTIVITY（咨询活动）
2. CHECK_CONSUME_BFT（检查消耗权益）
3. CHECK_RISK_BEFORE（检查前置风控）
4. DEDUCT_CONSUME_BFT（扣减消耗权益）
5. EXECUTE_BFT（执行权益）
6. CONFIRM_CONSUME_BFT（确认消耗权益）
7. REPORT_RISK_AFTER（上报后置风控）

## 注意事项

1. 中文、英文字母、数字之间需要留有一个空格
2. log 输出需要是英文
3. 错误信息要具体明确，便于问题排查
4. 监控打点使用 com.ddmc.equity.common.util.CsossUtils.logEventWithSpan 方法，type 使用统一的 MonitorConstants 常量
5. 所有处理器都需要添加 @Component 注解 
6. 处理器映射 key 格式必须为：sceneCode + "_" + processType 
7. 默认处理器的 getSceneCode 返回 "default"
8. 流程引擎的异常处理会构建详细的异常信息，包含异常步骤 
9. 步骤执行结果会自动合并到上下文，供后续步骤使用 
10. shouldBreakAfterProcess 默认逻辑为不成功时中断流程