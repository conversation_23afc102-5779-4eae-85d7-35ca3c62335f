# 风控处理器实现要求

## 任务目标
实现风控相关的处理器，包括前置风控检查和后置风控上报两个处理器。

## 前置依赖
- SceneActionProcessor 接口已定义
- SceneActionProcessContext 和 SceneActionProcessResult 已创建
- 风控相关的服务和 DTO 已实现

## 需要完成的代码

### 1. 前置风控检查处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.check_risk_before.DefaultCheckRiskBeforeProcessor`

#### 类定义
- 实现 SceneActionProcessor 接口
- 使用 @Slf4j 和 @Component 注解

#### 方法实现

##### getProcessTypeEnum 方法
- 返回：SceneActionProcessTypeEnum.CHECK_RISK_BEFORE

##### isEnabled 方法
- 参数：SceneActionProcessContext context
- 返回：boolean
- 实现逻辑： 
  1. 使用默认逻辑，不重写

##### process 方法
- 参数：SceneActionProcessContext context
- 返回：SceneActionProcessResult
- 参考代码：com.ddmc.equity.domain.service.core.impl.UniversalBenefitOperateCoreServiceImpl.draw
- 处理流程：
  1. 构建风控检查请求参数
  2. 调用 com.ddmc.equity.processor.risk.RiskControlFactory#process 进行前置检查
  3. 解析风控检查结果
  4. 风控通过，奖励不变。返回成功
  5. 风控不通过，不发奖励。返回失败（和 unableReceiveBenefitDTOList、riskControlInterceptRuleDTOList）
  6. 风控不通过，替换奖励。获取 getRiskReplaceBenefit，如果为空则返回失败，否则返回成功（和 riskReplaceBenefit、riskControlInterceptRuleDTOList）

### 2. 后置风控上报处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.report_risk_after.DefaultReportRiskAfterProcessor`

#### 类定义
- 实现 SceneActionProcessor 接口
- 使用 @Slf4j 和 @Component 注解

#### 配置属性
```java
@Value("${scene.action.risk.report.enabled:true}")
private Boolean riskReportEnabled;

@Value("${scene.action.risk.report.required.scene.codes:}")
private Set<String> riskReportRequiredSceneCodes;

@Value("${scene.action.risk.report.async:true}")
private Boolean riskReportAsync;
```

#### 依赖注入
```java
@Resource
private RiskControlService riskControlService;

@Resource
private ThreadPoolTaskExecutor riskReportExecutor;
```

#### 方法实现

##### getProcessTypeEnum 方法
- 返回：SceneActionProcessTypeEnum.REPORT_RISK_AFTER

##### isEnabled 方法
- 参数：SceneActionProcessContext context
- 返回：boolean
- 实现逻辑：
  1. 只有执行权益（EXECUTE_BFT）成功时才启用该节点（ProcessStatus.SUCCESS.equals(context.getBizData().getExecuteBftResultStatus())）

##### shouldChangeResult 方法
- 参数：SceneActionProcessContext context
- 返回：boolean
- 实现：返回 false（风控上报不影响流程结果）

##### shouldBreakAfterProcess 方法
- 参数：SceneActionProcessContext context, SceneActionProcessResult result
- 返回：boolean
- 实现：返回 false（风控上报不中断流程）

##### process 方法
- 参数：SceneActionProcessContext context
- 返回：SceneActionProcessResult
- 参考代码：com.ddmc.equity.domain.service.core.impl.UniversalBenefitOperateCoreServiceImpl.receive(com.ddmc.equity.domain.dto.DrawAndReceiveReqDTO, com.ddmc.equity.domain.dto.FullBenefitInfoDTO)
- 处理流程：
  1. 构建风控上报请求参数
  2. 异步上报（调用 com.ddmc.equity.processor.risk.RiskControlFactory.process）
  3. 返回成功结果（上报失败不影响主流程）

## 注意事项

1. 中文、英文字母、数字之间需要留有一个空格
2. log 输出需要是英文
3. 错误信息要具体明确，便于问题排查
4. 监控打点使用 com.ddmc.equity.common.util.CsossUtils.logEventWithSpan 方法，type 使用统一的 MonitorConstants 常量
5. 所有处理器都需要添加 @Component 注解 
6. 风控检查失败会中断流程，风控上报失败不影响流程